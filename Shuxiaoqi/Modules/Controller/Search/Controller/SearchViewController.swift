//
//  SearchViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/19.
//

import UIKit
import SnapKit

class SearchViewController: BaseViewController {
    
    // MARK: - 属性
    
    // 视图状态枚举
    private enum ViewState {
        case initial    // 初始状态：展示历史记录和热门推荐
        case results    // 结果状态：展示搜索结果
    }
    
    private var currentState: ViewState = .initial
    private let MAX_HISTORY_COUNT = 15 // 最多保存15条历史记录
    
    // 模拟搜索历史数据 - 将改为从本地存储读取
    private var searchHistoryData: [String] = []
    
    // 热门推荐数据 – 由接口动态获取
    private var hotRecommendData: [String] = []
    
    // 当前显示的是哪一批推荐数据
    private var isShowingAlternativeRecommends = false
    
    private var searchData: [[String: String]] = []
    
    // MARK: - Pagination & State
    private let initialPage = 0
    private let defaultPageSize = 20 // 每页加载数量
    private var currentPage: Int = 0
    private var isLoadingData: Bool = false // 包括刷新和加载更多
    private var canLoadMore: Bool = true
    private var currentSearchKeywords: String = ""
    private var currentSortOption: Int? = nil // 默认综合排序
    
    // MARK: - 子控制器集成属性
    private var categoryViewControllers: [UIViewController] = []
    private var currentCategoryIndex: Int = 0
    
    // MARK: - UI组件
    
    // 初始状态视图容器
    private lazy var initialStateView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 搜索历史标题视图
    private lazy var historyHeaderView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 搜索历史标题
    private lazy var historyTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "搜索历史"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 清除历史按钮
    private lazy var clearHistoryButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "icon_trash") ?? UIImage(systemName: "trash"), for: .normal)
        button.tintColor = UIColor(hex: "#999999")
        button.addTarget(self, action: #selector(clearHistoryButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 搜索历史流式布局容器
    private lazy var historyFlowView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.showsVerticalScrollIndicator = false
        scrollView.backgroundColor = .white
        return scrollView
    }()
    
    // 热门推荐标题
    private lazy var hotRecommendLabel: UILabel = {
        let label = UILabel()
        label.text = "热门推荐"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 热门推荐标题旁的换一批按钮
    private lazy var refreshButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("换一批", for: .normal)
        button.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        
        // 添加刷新图标
        if let refreshImage = UIImage(named: "icon_refresh") ?? UIImage(systemName: "arrow.clockwise") {
            let tintedImage = refreshImage.withRenderingMode(.alwaysTemplate)
            button.setImage(tintedImage, for: .normal)
            button.tintColor = UIColor(hex: "#FF6236") // 设置为红色
            button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 5) // 图标和文字间距
        }
        
        button.addTarget(self, action: #selector(refreshRecommendsButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 查看全部按钮
    private lazy var viewAllButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("查看全部", for: .normal)
        button.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(viewAllButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 热门推荐流式布局容器
    private lazy var hotRecommendFlowView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 结果状态视图容器
    private lazy var resultsStateView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.isHidden = true
        return view
    }()
    
    // 导航栏容器
    private lazy var navigationContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 渐变边框视图
    private lazy var searchBoxView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 18  // 圆角
        
        // 添加细微阴影效果
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 1)
        view.layer.shadowRadius = 2
        view.layer.shadowOpacity = 0.5
        
        return view
    }()
    
    // 搜索图标
    private lazy var searchIconView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "search_icon"))
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = UIColor.lightGray  // 设置默认颜色，如果图标支持tint
        return imageView
    }()
    
    // 搜索文本框 - 改为自定义风格
    private lazy var searchTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "搜索感兴趣的视频"
        textField.font = UIFont.systemFont(ofSize: 14)
        textField.borderStyle = .none
        textField.backgroundColor = .clear
        textField.returnKeyType = .search
        textField.clearButtonMode = .whileEditing
        //修改指示器颜色
        textField.tintColor = .orange
        return textField
    }()
    
    // 搜索按钮
    private lazy var searchButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("搜索", for: .normal)
        button.setTitleColor(UIColor(searchHex: "#FF6236"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(searchButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 新增: 分类内容容器，避免子控制器遮挡顶部Tab
    private lazy var categoryContentContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        return view
    }()
    
    // 替换表格视图为集合视图
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        
        let width = (UIScreen.main.bounds.width - 12 * 3) / 2  // 两列布局，左右边距12，中间间距10
        let height: CGFloat = 376  // 设置合适的高度
        layout.itemSize = CGSize(width: width, height: height)
        layout.sectionInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor(hex: "#F5F5F5")
        collectionView.register(SearchResultCell.self, forCellWithReuseIdentifier: "SearchResultCell")
        collectionView.dataSource = self
        collectionView.delegate = self
        // 添加下拉刷新控件
        collectionView.refreshControl = refreshControl
        return collectionView
    }()
    
    // 下拉刷新控件
    private lazy var refreshControl: UIRefreshControl = {
        let control = UIRefreshControl()
        control.addTarget(self, action: #selector(handleRefresh(_:)), for: .valueChanged)
        return control
    }()
    
    // 返回按钮
    private lazy var customBackButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "nav_back") ?? UIImage(systemName: "chevron.left"), for: .normal)
        button.addTarget(self, action: #selector(customBackButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 分类标签栏
    private lazy var categoryTabsScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.backgroundColor = .white
        return scrollView
    }()
    
    // 分类标签容器
    private lazy var categoryTabsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 下划线指示器
    private lazy var tabIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(searchHex: "#FF6236")
        view.layer.cornerRadius = 3
        return view
    }()
    
    // 分类标签列表
    private lazy var categoryButtons: [UIButton] = {
        let categories = ["视频", "用户", "商品", "商家", "团购", "外卖"]
        var buttons: [UIButton] = []
        for (index, category) in categories.enumerated() {
            let button = UIButton(type: .custom)
            button.setTitle(category, for: .normal)
            button.setTitleColor(UIColor(searchHex: "#333333"), for: .normal)
            button.titleLabel?.font = .systemFont(ofSize: 16)
            button.tag = index
            button.layer.cornerRadius = 15
            button.contentEdgeInsets = UIEdgeInsets(top: 6, left: 12, bottom: 6, right: 12)
            button.addTarget(self, action: #selector(categoryButtonTapped(_:)), for: .touchUpInside)
            buttons.append(button)
        }
        return buttons
    }()
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 从本地存储加载搜索历史，如无则请求远端
        loadSearchHistory()
        // 暂时屏蔽云端同步功能，避免删除本地历史后重复加载云端数据导致展示异常
        // if searchHistoryData.isEmpty {
        //     fetchSearchHistoryFromAPI()
        // }

        // 请求热门推荐词
        fetchHotRecommend()

        // 初始化子控制器
        setupCategoryViewControllers()
        
        setupUI()
        setupInitialStateView()
        setupResultsStateView()
        
        // 应用初始状态
        switchToState(.initial)
        
        // 默认显示第一个分类内容
        switchToCategory(at: 0)
        
        // 应用初始选中状态
        DispatchQueue.main.async {
            if let firstCategoryButton = self.categoryButtons.first {
                self.applyGradientTextToButton(firstCategoryButton)
                firstCategoryButton.titleLabel?.font = .boldSystemFont(ofSize: 16)
            }
            // 已移除公共二级筛选，首个分类按钮仅需文字渐变，不应用背景渐变
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 自定义代码
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 确保在布局完成后更新渐变边框和按钮渐变
        DispatchQueue.main.async {
            self.updateSearchBoxGradientBorder()
            
            // 更新所有分类按钮的渐变层框架
            for button in self.categoryButtons {
                button.layer.sublayers?.forEach { layer in
                    if layer.name == "gradientLayer" {
                        layer.frame = button.bounds
                    }
                }
            }
            
            // 确保选中的分类按钮文字渐变正确显示
            for button in self.categoryButtons {
                if button.attributedTitle(for: .normal) != nil {
                    // 如果按钮已经有属性文本，重新应用渐变
                    self.applyGradientTextToButton(button)
                }
            }
        }
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        view.backgroundColor = .white
        
        // 导航栏设置
        view.addSubview(navigationContainer)
        navigationContainer.addSubview(customBackButton)
        navigationContainer.addSubview(searchBoxView)
        navigationContainer.addSubview(searchButton)
        
        // 在搜索框内添加搜索图标和文本框
        searchBoxView.addSubview(searchIconView)
        searchBoxView.addSubview(searchTextField)
        
        // 设置导航容器约束
        navigationContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(navigationBarHeight() + statusBarHeight())
        }
        
        // 设置返回按钮约束
        customBackButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.bottom.equalToSuperview().offset(-12)
            make.width.height.equalTo(24)
        }
        
        // 设置搜索按钮约束
        searchButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalTo(customBackButton)
            make.width.equalTo(40)
        }
        
        // 设置搜索框约束
        searchBoxView.snp.makeConstraints { make in
            make.left.equalTo(customBackButton.snp.right).offset(12)
            make.right.equalTo(searchButton.snp.left).offset(-12)
            make.centerY.equalTo(customBackButton)
            make.height.equalTo(36)
        }
        
        // 设置搜索图标约束
        searchIconView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(10)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        // 设置搜索文本框约束
        searchTextField.snp.makeConstraints { make in
            make.left.equalTo(searchIconView.snp.right).offset(8)
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().offset(-10)
        }
        
        // 设置文本框代理
        searchTextField.delegate = self
        
        // 添加初始状态视图和结果状态视图
        view.addSubview(initialStateView)
        view.addSubview(resultsStateView)
        
        initialStateView.snp.makeConstraints { make in
            make.top.equalTo(navigationContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        resultsStateView.snp.makeConstraints { make in
            make.top.equalTo(navigationContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    private func setupInitialStateView() {
        // 添加搜索历史标题视图
        initialStateView.addSubview(historyHeaderView)
        historyHeaderView.addSubview(historyTitleLabel)
        historyHeaderView.addSubview(clearHistoryButton)
        
        historyHeaderView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview()
            make.height.equalTo(24)
        }
        
        historyTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }
        
        clearHistoryButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // 添加搜索历史流式布局
        initialStateView.addSubview(historyFlowView)
        historyFlowView.snp.makeConstraints { make in
            make.top.equalTo(historyHeaderView.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            // 初始设置一个默认高度，后续会根据内容重新设置
            make.height.equalTo(0)
        }
        
        // 创建并添加搜索历史标签
        setupHistoryTags()
        
        // 添加热门推荐标题
        initialStateView.addSubview(hotRecommendLabel)
        initialStateView.addSubview(refreshButton) // 使用换一批按钮替代查看全部
        
        hotRecommendLabel.snp.makeConstraints { make in
            make.top.equalTo(historyFlowView.snp.bottom).offset(24)
            make.left.equalToSuperview().offset(16)
        }
        
        refreshButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalTo(hotRecommendLabel)
        }
        
        // 添加热门推荐流式布局
        initialStateView.addSubview(hotRecommendFlowView)
        hotRecommendFlowView.snp.makeConstraints { make in
            make.top.equalTo(hotRecommendLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            // 初始设置一个默认高度，后续会根据内容重新设置
            make.height.equalTo(0)
        }
        
        // 创建并添加热门推荐标签
        setupHotRecommendTags()
    }
    
    private func setupResultsStateView() {
        // 分类标签栏设置
        resultsStateView.addSubview(categoryTabsScrollView)
        categoryTabsScrollView.addSubview(categoryTabsContainer)
        
        categoryTabsScrollView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        categoryTabsContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(categoryTabsScrollView)
        }
        
        // 添加分类按钮，设置指示器
        var previousButton: UIButton?
        let buttonWidth: CGFloat = 65
        
        for (index, button) in categoryButtons.enumerated() {
            categoryTabsContainer.addSubview(button)
            
            button.snp.makeConstraints { make in
                if let prev = previousButton {
                    make.left.equalTo(prev.snp.right)
                } else {
                    make.left.equalToSuperview().offset(12)
                }
                make.centerY.equalToSuperview()
                make.width.equalTo(buttonWidth)
                make.height.equalTo(44)
            }
            
            previousButton = button
            
            // 选中第一个分类，添加指示器
            if index == 0 {
                categoryTabsContainer.addSubview(tabIndicator)
                tabIndicator.snp.makeConstraints { make in
                    make.bottom.equalToSuperview().offset(-3)
                    make.centerX.equalTo(button)
                    make.width.height.equalTo(6)
                }
            }
        }
        
        // 设置最后一个按钮的约束
        if let lastButton = previousButton {
            lastButton.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-12)
            }
        }
        
        // 新增: 分类内容容器
        resultsStateView.addSubview(categoryContentContainer)
        categoryContentContainer.snp.makeConstraints { make in
            make.top.equalTo(categoryTabsScrollView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        // 将 collectionView 添加到内容容器
        categoryContentContainer.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // 创建搜索历史标签
    private func setupHistoryTags() {
        // 清除旧的标签
        for view in historyFlowView.subviews {
            view.removeFromSuperview()
        }
        
        // 如果历史为空，不显示任何内容
        if searchHistoryData.isEmpty {
            // 使用remakeConstraints而不是updateConstraints
            historyFlowView.snp.remakeConstraints { make in
                make.top.equalTo(historyHeaderView.snp.bottom).offset(12)
                make.left.right.equalToSuperview()
                make.height.equalTo(0)
            }
            return
        }
        
        let tagContainer = UIView()
        historyFlowView.addSubview(tagContainer)
        
        // 先添加到容器中，然后计算所需的高度
        var xPosition: CGFloat = 0
        var yPosition: CGFloat = 0
        let maxWidth = UIScreen.main.bounds.width - 32
        let tagHeight: CGFloat = 30
        let horizontalMargin: CGFloat = 10
        let verticalMargin: CGFloat = 10
        
        for (index, historyItem) in searchHistoryData.enumerated() {
            let tagButton = createTagButton(withTitle: historyItem, isHistory: true, tag: index)
            
            // 计算标签的宽度 - 增加宽度以适应内容
            let title = historyItem as NSString
            let titleFont = UIFont.systemFont(ofSize: 14)
            let titleWidth = title.size(withAttributes: [.font: titleFont]).width
            
            // 增加额外空间，确保有足够的空间显示文本和关闭按钮
            // 24(左右内边距) + 20(关闭按钮区域) + titleWidth + 10(额外安全空间)
            let tagWidth = min(titleWidth + 54, maxWidth - 10)
            
            // 检查是否需要换行
            if xPosition + tagWidth > maxWidth {
                xPosition = 0
                yPosition += tagHeight + verticalMargin
            }
            
            tagButton.frame = CGRect(x: xPosition, y: yPosition, width: tagWidth, height: tagHeight)
            tagContainer.addSubview(tagButton)
            
            xPosition += tagWidth + horizontalMargin
        }
        
        // 设置容器大小
        let totalHeight = yPosition + tagHeight
        tagContainer.frame = CGRect(x: 16, y: 0, width: maxWidth, height: totalHeight)
        
        // 设置滚动视图的内容大小
        historyFlowView.contentSize = CGSize(width: UIScreen.main.bounds.width, height: totalHeight)
        
        // 使用remakeConstraints而不是updateConstraints，避免约束冲突
        historyFlowView.snp.remakeConstraints { make in
            make.top.equalTo(historyHeaderView.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.height.equalTo(totalHeight)
        }
    }
    
    // 创建热门推荐标签
    private func setupHotRecommendTags() {
        // 清除旧的标签
        for view in hotRecommendFlowView.subviews {
            view.removeFromSuperview()
        }
        
        // 使用接口返回的热门推荐数据
        let dataToShow = hotRecommendData
        
        // 与搜索历史相同的流式布局逻辑
        var xPosition: CGFloat = 16
        var yPosition: CGFloat = 0
        let maxWidth = UIScreen.main.bounds.width - 32
        let tagHeight: CGFloat = 30
        let horizontalMargin: CGFloat = 10
        let verticalMargin: CGFloat = 10
        
        for (index, recommendItem) in dataToShow.enumerated() {
            let tagButton = createTagButton(withTitle: recommendItem, isHistory: false, tag: 100 + index)
            
            // 计算标签的宽度
            let title = recommendItem as NSString
            let titleWidth = title.size(withAttributes: [.font: UIFont.systemFont(ofSize: 14)]).width
            let tagWidth = min(titleWidth + 24, maxWidth - 16) // 限制最大宽度
            
            // 检查是否需要换行
            if xPosition + tagWidth > maxWidth {
                xPosition = 16
                yPosition += tagHeight + verticalMargin
            }
            
            tagButton.frame = CGRect(x: xPosition, y: yPosition, width: tagWidth, height: tagHeight)
            hotRecommendFlowView.addSubview(tagButton)
            
            xPosition += tagWidth + horizontalMargin
        }
        
        // 计算总高度
        let totalHeight = yPosition + tagHeight
        
        // 使用remakeConstraints而不是updateConstraints，避免约束冲突
        hotRecommendFlowView.snp.remakeConstraints { make in
            make.top.equalTo(hotRecommendLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.height.equalTo(totalHeight)
        }
    }
    
    // 创建标签按钮
    private func createTagButton(withTitle title: String, isHistory: Bool, tag: Int) -> UIButton {
        let button = UIButton(type: .custom)
        button.setTitle(title, for: .normal)
        button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.backgroundColor = UIColor(hex: "#F5F5F5")
        button.layer.cornerRadius = 15
        
        // 为历史标签增加右侧内边距（从28调整为36），保持图标视觉大小不变但扩大点击区域
        button.contentEdgeInsets = UIEdgeInsets(top: 5, left: 12, bottom: 5, right: isHistory ? 36 : 12)
        button.titleLabel?.lineBreakMode = .byTruncatingTail
        button.tag = tag
        
        // 如果是历史标签，添加删除图标
        if isHistory {
            // 创建独立的删除按钮
            let deleteButton = UIButton(type: .custom)
            deleteButton.setImage(UIImage(named: "icon_close") ?? UIImage(systemName: "xmark"), for: .normal)
            deleteButton.tintColor = UIColor(hex: "#999999")
            deleteButton.contentMode = .scaleAspectFit
            deleteButton.tag = tag  // 复用父按钮的tag
            
            // 修改: 使用自动布局替代frame设置
            deleteButton.translatesAutoresizingMaskIntoConstraints = false
            button.addSubview(deleteButton)
            
            // 添加约束：垂直居中，右侧内边距8（保持原有值，通过增加父按钮的contentEdgeInsets实现点击范围扩大）
            NSLayoutConstraint.activate([
                deleteButton.centerYAnchor.constraint(equalTo: button.centerYAnchor),
                deleteButton.trailingAnchor.constraint(equalTo: button.trailingAnchor, constant: -8),
                deleteButton.widthAnchor.constraint(equalToConstant: 24),
                deleteButton.heightAnchor.constraint(equalToConstant: 24)
            ])
            
            // 修改: 修复事件冒泡问题
            deleteButton.addTarget(self, action: #selector(deleteHistoryTagTapped(_:)), for: .touchUpInside)
            
            // 修改: 移除父按钮的点击事件，仅保留删除按钮事件
            button.addTarget(self, action: #selector(historyTagTapped(_:)), for: .touchUpInside)
        } else {
            // 热门推荐点击事件
            button.addTarget(self, action: #selector(recommendTagTapped(_:)), for: .touchUpInside)
        }
        
        return button
    }
    
    // 新增：处理历史标签的删除按钮点击
    @objc private func deleteHistoryTagTapped(_ sender: UIButton) {
        // 阻止事件冒泡到父按钮
        sender.superview?.endEditing(true)
        
        // 获取要删除的索引
        let index = sender.tag
        if index >= 0 && index < searchHistoryData.count {
            // 删除指定历史记录
            searchHistoryData.remove(at: index)
            saveSearchHistory()
            setupHistoryTags()
        }
    }
    
    // 切换视图状态
    private func switchToState(_ state: ViewState) {
        currentState = state
        
        switch state {
        case .initial:
            initialStateView.isHidden = false
            resultsStateView.isHidden = true
        case .results:
            initialStateView.isHidden = true
            resultsStateView.isHidden = false
        }
    }
    
    // MARK: - 事件处理
    
    @objc private func customBackButtonTapped() {
        // 根据当前的展示方式选择不同的返回方式
        if let navigationController = navigationController {
            // 如果有导航控制器且不是根视图控制器，使用pop方式
            if navigationController.viewControllers.count > 1 {
                navigationController.popViewController(animated: true)
            } else {
                // 如果是根视图控制器但在导航控制器中，可能是模态展示的导航控制器
                dismiss(animated: true)
            }
        } else {
            // 如果没有导航控制器，直接dismiss
            dismiss(animated: true)
        }

        print("[SearchViewController] 返回按钮被点击，使用了适当的返回方式")
    }
    
    @objc private func searchButtonTapped() {
        // 执行搜索操作
        if let searchText = searchTextField.text, !searchText.isEmpty {
            self.currentSearchKeywords = searchText
            self.currentPage = initialPage // 重置页码
            self.canLoadMore = true      // 重置加载更多状态
            self.currentSortOption = nil // 新搜索默认为综合排序

            // 添加当前搜索词到历史记录（关键修改点）
            addSearchHistory(searchText) // <<<< 新增此行代码
            
            // 直接将关键词交给当前分类子控制器，由其决定如何请求
            if let searchableVC = categoryViewControllers[currentCategoryIndex] as? Searchable {
                searchableVC.search(with: searchText)
            }

            // 切换到结果状态
            switchToState(.results)
        }

        // 隐藏键盘
        searchTextField.resignFirstResponder()
    }
    
    @objc private func clearHistoryButtonTapped() {
        // 清除搜索历史
        searchHistoryData.removeAll()
        saveSearchHistory()
        
        // 更新UI
        setupHistoryTags()
    }
    
    @objc private func viewAllButtonTapped() {
        // 查看全部热门推荐
        print("查看全部热门推荐")
    }
    
    @objc private func historyTagTapped(_ sender: UIButton) {
        // 点击历史标签时，执行搜索
        if let title = sender.title(for: .normal) {
            searchTextField.text = title
            addSearchHistory(title) // 将点击的历史移动到最前面
            
            self.currentSearchKeywords = title
            self.currentPage = initialPage
            self.canLoadMore = true
            self.currentSortOption = nil // 新搜索默认为综合排序
            self.searchData.removeAll()
            self.collectionView.reloadData()
            
            fetchSearchResults(keywords: title, requestedPage: initialPage, size: defaultPageSize, sort: self.currentSortOption)
            switchToState(.results)
        }
    }
    
    @objc private func recommendTagTapped(_ sender: UIButton) {
        // 点击推荐标签时，执行搜索
        if let title = sender.title(for: .normal) {
            searchTextField.text = title
            addSearchHistory(title) // 添加到搜索历史
            
            self.currentSearchKeywords = title
            self.currentPage = initialPage
            self.canLoadMore = true
            self.currentSortOption = nil // 新搜索默认为综合排序
            self.searchData.removeAll()
            self.collectionView.reloadData()
            
            fetchSearchResults(keywords: title, requestedPage: initialPage, size: defaultPageSize, sort: self.currentSortOption)
            switchToState(.results)
        }
    }
    
    @objc private func categoryButtonTapped(_ sender: UIButton) {
        // 切换选中分类
        for button in categoryButtons {
            if button.tag == sender.tag {
                // 选中状态 - 文字渐变
                applyGradientTextToButton(button)
                button.titleLabel?.font = .boldSystemFont(ofSize: 16)
                // 移除背景色
                button.backgroundColor = .clear
                removeGradientFromButton(button)
            } else {
                // 未选中状态 - 需要恢复原始标题（不能只修改颜色）
                if let title = button.currentTitle ?? button.attributedTitle(for: .normal)?.string {
                    // 移除属性文本，使用普通文本
                    button.setAttributedTitle(nil, for: .normal)
                    button.setTitle(title, for: .normal)
                    button.setTitleColor(UIColor(searchHex: "#333333"), for: .normal)
                    button.titleLabel?.font = .systemFont(ofSize: 16)
                }
                // 确保没有渐变背景
                removeGradientFromButton(button)
                button.backgroundColor = .clear
            }
        }
        
        // 动画移动指示器 - 保持圆点在与原下划线相同的位置
        UIView.animate(withDuration: 0.3) {
            self.tabIndicator.snp.remakeConstraints { make in
                make.bottom.equalTo(self.categoryTabsContainer).offset(-3)
                make.centerX.equalTo(sender)
                make.width.height.equalTo(6)
            }
            self.view.layoutIfNeeded()
        }
        // 新增：切换内容区子控制器
        switchToCategory(at: sender.tag)
    }
    
    // 获取状态栏高度
    private func statusBarHeight() -> CGFloat {
        if #available(iOS 15.0, *) {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let statusBarManager = windowScene.statusBarManager {
                return statusBarManager.statusBarFrame.height
            }
            return 44
        } else if #available(iOS 13.0, *) {
            return UIApplication.shared.windows.first?.windowScene?.statusBarManager?.statusBarFrame.height ?? 44
        } else {
            return UIApplication.shared.statusBarFrame.height
        }
    }
    
    // 获取导航栏高度
    private func navigationBarHeight() -> CGFloat {
        return 44
    }
    
    // 添加搜索框渐变边框更新方法
    private func updateSearchBoxGradientBorder() {
        guard searchBoxView.bounds.width > 0 && searchBoxView.bounds.height > 0 else { return }
        // 设置1pt纯色边框，颜色采用主题色 #FF7F41
        searchBoxView.layer.borderWidth = 1
        searchBoxView.layer.borderColor = UIColor(searchHex: "#FF7F41").cgColor
        searchBoxView.layer.cornerRadius = 18
        // 移除可能遗留的渐变容器子视图
        for sub in searchBoxView.subviews {
            if sub !== searchIconView && sub !== searchTextField {
                sub.removeFromSuperview()
            }
        }
    }
    
    // 添加渐变背景到按钮的方法
    private func applyGradientToButton(_ button: UIButton) {
        // 移除旧的渐变层
        button.layer.sublayers?.forEach { layer in
            if layer is CAGradientLayer {
                layer.removeFromSuperlayer()
            }
        }
        
        // 创建新的渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = button.bounds
        gradientLayer.colors = [
            UIColor(searchHex: "#FF7F41").cgColor,
            UIColor(searchHex: "#FF5151").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 1.0, y: 0.0)
        gradientLayer.cornerRadius = 15 // 圆角
        gradientLayer.name = "gradientLayer" // 用于识别
        
        // 插入渐变层到按钮的最底层
        button.layer.insertSublayer(gradientLayer, at: 0)
    }
    
    // 从按钮移除渐变背景的方法
    private func removeGradientFromButton(_ button: UIButton) {
        button.layer.sublayers?.forEach { layer in
            if layer.name == "gradientLayer" {
                layer.removeFromSuperlayer()
            }
        }
        button.backgroundColor = .clear
    }
    
    // 添加一个新方法来为按钮文字应用渐变效果
    private func applyGradientTextToButton(_ button: UIButton) {
        // 确保按钮有标题文本
        guard let title = button.title(for: .normal) else { return }
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = button.titleLabel?.bounds ?? CGRect(x: 0, y: 0, width: 100, height: 30)
        gradientLayer.colors = [
            UIColor(searchHex: "#FF7F41").cgColor,
            UIColor(searchHex: "#FF5151").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1.0, y: 0.5)
        
        // 创建一个UIImage从渐变层
        UIGraphicsBeginImageContextWithOptions(gradientLayer.frame.size, false, UIScreen.main.scale)
        if let context = UIGraphicsGetCurrentContext() {
            gradientLayer.render(in: context)
            if let gradientImage = UIGraphicsGetImageFromCurrentImageContext() {
                UIGraphicsEndImageContext()
                
                // 创建一个新的带有渐变的文本属性
                let attributedString = NSAttributedString(
                    string: title,
                    attributes: [
                        .font: UIFont.boldSystemFont(ofSize: 16),
                        .foregroundColor: UIColor(patternImage: gradientImage)
                    ]
                )
                
                // 设置按钮的属性文本
                button.setAttributedTitle(attributedString, for: .normal)
            } else {
                UIGraphicsEndImageContext()
            }
        } else {
            UIGraphicsEndImageContext()
            // 如果渐变创建失败，至少设置一个主题色
            button.setTitleColor(UIColor(searchHex: "#FF5151"), for: .normal)
        }
    }
    
    // MARK: - 搜索历史存储和管理
    
    // 加载搜索历史
    private func loadSearchHistory() {
        if let savedHistory = UserDefaults.standard.array(forKey: "searchHistory") as? [String] {
            searchHistoryData = savedHistory
        }
    }
    
    // 保存搜索历史
    private func saveSearchHistory() {
        UserDefaults.standard.set(searchHistoryData, forKey: "searchHistory")
        UserDefaults.standard.synchronize()
    }
    
    // 添加搜索历史
    private func addSearchHistory(_ text: String) {
        // 检查是否已存在，如果存在则移除旧的
        if let index = searchHistoryData.firstIndex(of: text) {
            searchHistoryData.remove(at: index)
        }
        
        // 添加到头部
        searchHistoryData.insert(text, at: 0)
        
        // 如果超出限制，移除最旧的
        if searchHistoryData.count > MAX_HISTORY_COUNT {
            searchHistoryData = Array(searchHistoryData.prefix(MAX_HISTORY_COUNT))
        }
        
        // 保存到本地
        saveSearchHistory()
        
        // 更新UI
        setupHistoryTags()
    }
    
    // 换一批推荐
    @objc private func refreshRecommendsButtonTapped() {
        // 重新请求热门推荐词
        fetchHotRecommend()
        
        // 添加旋转动画效果
        let rotationAnimation = CABasicAnimation(keyPath: "transform.rotation.z")
        rotationAnimation.toValue = CGFloat.pi * 2
        rotationAnimation.duration = 0.5
        rotationAnimation.isCumulative = true
        rotationAnimation.repeatCount = 1
        
        if let imageView = refreshButton.imageView {
            imageView.layer.add(rotationAnimation, forKey: "rotationAnimation")
        }
    }
    
    // MARK: - 下拉刷新处理
    @objc private func handleRefresh(_ sender: UIRefreshControl) {
        guard !currentSearchKeywords.isEmpty else {
            sender.endRefreshing(); return
        }
        if let searchableVC = categoryViewControllers[currentCategoryIndex] as? Searchable {
            searchableVC.search(with: currentSearchKeywords)
        }
        sender.endRefreshing()
    }
    
    // MARK: - 子控制器初始化
    private func setupCategoryViewControllers() {
        let videoVC = SearchVideoViewController()
        let userVC = SearchUserViewController()
        let productVC = SearchProductViewController()
        let merchantVC = SearchMerchantViewController()
        let groupBuyVC = SearchGroupBuyViewController()
        let takeoutVC = SearchTakeoutViewController()
        categoryViewControllers = [videoVC, userVC, productVC, merchantVC, groupBuyVC, takeoutVC]
    }
    
    // MARK: - 分类内容切换
    private func switchToCategory(at index: Int) {
        // 只在resultsStateView显示时切换内容
        guard resultsStateView.superview != nil else { return }
        // 移除当前子控制器
        if categoryViewControllers.indices.contains(currentCategoryIndex) {
            let currentVC = categoryViewControllers[currentCategoryIndex]
            if currentVC.parent == self {
                currentVC.willMove(toParent: nil)
                currentVC.view.removeFromSuperview()
                currentVC.removeFromParent()
            }
        }
        // 添加新子控制器
        if categoryViewControllers.indices.contains(index) {
            let newVC = categoryViewControllers[index]
            addChild(newVC)
            categoryContentContainer.addSubview(newVC.view)
            newVC.view.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            newVC.didMove(toParent: self)
            currentCategoryIndex = index

            // SearchViewController 自身不再承载结果列表
            collectionView.isHidden = true

            // 如果已存在关键词，切换到新分类后立即刷新
            if let searchableVC = newVC as? Searchable, !currentSearchKeywords.isEmpty {
                searchableVC.search(with: currentSearchKeywords)
            }
        }
    }
    
    // MARK: - API: 热门推荐词
    private func fetchHotRecommend() {
        APIManager.shared.listSearchRecommend(params: [:]) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    self.hotRecommendData = response.data.map { $0.names }
                    self.setupHotRecommendTags()
                case .failure(let error):
                    print("热门推荐词 API 错误: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - API: 搜索历史词
    private func fetchSearchHistoryFromAPI() {
        APIManager.shared.listSearchHistory(params: [:]) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    self.searchHistoryData = response.data.map { $0.names }
                    self.saveSearchHistory()
                    self.setupHistoryTags()
                case .failure(let error):
                    print("搜索历史词 API 错误: \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - UICollectionViewDataSource
extension SearchViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return searchData.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "SearchResultCell", for: indexPath) as! SearchResultCell
        cell.configure(with: searchData[indexPath.row])
        
        // 为第一个单元格添加消息小红点
//        if indexPath.row == 0 {
//            cell.showBadge()
//        }
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension SearchViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 处理点击搜索结果的逻辑
        print("选择了搜索结果: \(searchData[indexPath.row]["title"] ?? "")")
    }
    
    // MARK: - UIScrollViewDelegate for Load More
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 如果不是 collectionView 在滚动，则忽略 (例如内部的横向滚动视图)
        guard scrollView == self.collectionView else { return }

        let offsetY = scrollView.contentOffset.y
        let contentHeight = scrollView.contentSize.height
        let scrollViewHeight = scrollView.frame.size.height

        // 检查是否滚动到接近底部，并且可以加载更多，并且当前没有正在加载数据，并且有数据存在
        if offsetY > contentHeight - scrollViewHeight * 1.5 && canLoadMore && !isLoadingData && !searchData.isEmpty {
            print("尝试加载更多数据，当前页: \(currentPage)，下一页: \(currentPage + 1)")
            fetchSearchResults(keywords: currentSearchKeywords, requestedPage: currentPage + 1, size: defaultPageSize, sort: self.currentSortOption)
        }
    }
}

// MARK: - 搜索结果单元格
class SearchResultCell: UICollectionViewCell {
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 10
        view.clipsToBounds = true
        return view
    }()
    
    private lazy var thumbnailImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor(hex: "#EEEEEE")
        imageView.image = UIImage(named: "t002")
        return imageView
    }()
    
    private lazy var durationLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 12)
        label.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        label.textAlignment = .center
        label.layer.cornerRadius = 4
        label.clipsToBounds = true
        return label
    }()
    
    private lazy var typeLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 11)
        label.textAlignment = .center
        label.layer.cornerRadius = 2
        label.clipsToBounds = true
        return label
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var authorAvatarView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor(hex: "#EEEEEE")
        imageView.layer.cornerRadius = 8 // 设置为宽高的一半，使其成为圆形
        return imageView
    }()
    
    private lazy var authorLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    private lazy var badgeView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.red
        view.layer.cornerRadius = 4
        view.isHidden = true
        return view
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(containerView)
        containerView.addSubview(thumbnailImageView)
        containerView.addSubview(durationLabel)
        containerView.addSubview(typeLabel)
        containerView.addSubview(titleLabel)
        containerView.addSubview(authorAvatarView)
        containerView.addSubview(authorLabel)
        thumbnailImageView.addSubview(badgeView)
        
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        thumbnailImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(298)
            make.width.equalTo(179)
        }
        
        durationLabel.snp.makeConstraints { make in
            make.right.equalTo(thumbnailImageView).offset(-8)
            make.bottom.equalTo(thumbnailImageView).offset(-8)
            make.height.equalTo(18)
            make.width.greaterThanOrEqualTo(40)
        }
        
        typeLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(8)
            make.top.equalTo(thumbnailImageView.snp.bottom).offset(8)
            make.height.equalTo(18)
            make.width.equalTo(36)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(typeLabel.snp.right).offset(6)
            make.centerY.equalTo(typeLabel)
            make.right.equalToSuperview().offset(-8)
        }
        
        authorAvatarView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(8)
            make.top.equalTo(typeLabel.snp.bottom).offset(6)
            make.width.height.equalTo(16)
        }
        
        authorLabel.snp.makeConstraints { make in
            make.left.equalTo(authorAvatarView.snp.right).offset(6)
            make.centerY.equalTo(authorAvatarView)
            make.right.equalToSuperview().offset(-8)
        }
        
        badgeView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.right.equalToSuperview().offset(-8)
            make.width.height.equalTo(8)
        }
    }
    
    func configure(with data: [String: String]) {
        titleLabel.text = data["title"]
        durationLabel.text = data["duration"]
        authorLabel.text = data["author"]
        thumbnailImageView.kf.setImage(with: URL(string: data["worksCoverImg"] ?? ""))
        // 设置类型标签的颜色
        if let type = data["type"] {
            typeLabel.text = type
            switch type {
            case "热门":
                typeLabel.backgroundColor = UIColor(hex: "#FF6236")
            case "推荐":
                typeLabel.backgroundColor = UIColor(hex: "#32CD32")
            case "最新":
                typeLabel.backgroundColor = UIColor(hex: "#4169E1")
            default:
                typeLabel.backgroundColor = UIColor(hex: "#888888")
            }
        }
        
        // 设置默认头像，通常会从网络加载
        // 这里只是示例，实际应用中应该从数据中获取头像URL并加载
        authorAvatarView.image = UIImage(named: "default_avatar")
    }
    
    func showBadge() {
        badgeView.isHidden = false
    }
    
    func hideBadge() {
        badgeView.isHidden = true
    }
}

// MARK: - UITextFieldDelegate
extension SearchViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        // 处理搜索
        searchButtonTapped()
        return true
    }
    
    func textFieldDidBeginEditing(_ textField: UITextField) {
        // 当文本框获得焦点时，确保显示初始状态，无论当前状态和文本内容如何
        if currentState == .results {
            switchToState(.initial)
        }
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        // 文本框失去焦点，如果有内容则切换到结果状态
        if !(textField.text?.isEmpty ?? true) && currentState == .initial {
            switchToState(.results)
        }
    }
}

// MARK: - UIColor扩展
extension UIColor {
    // 更改方法名，避免冲突
    convenience init(searchHex: String) {
        var hexSanitized = searchHex.trimmingCharacters(in: .whitespacesAndNewlines)
        hexSanitized = hexSanitized.replacingOccurrences(of: "#", with: "")
        
        var rgb: UInt64 = 0
        
        Scanner(string: hexSanitized).scanHexInt64(&rgb)
        
        let red = CGFloat((rgb & 0xFF0000) >> 16) / 255.0
        let green = CGFloat((rgb & 0x00FF00) >> 8) / 255.0
        let blue = CGFloat(rgb & 0x0000FF) / 255.0
        
        self.init(red: red, green: green, blue: blue, alpha: 1.0)
    }
}

// MARK: - 通用搜索结果请求（兼容旧逻辑）
private extension SearchViewController {
    /// 旧版本列表直接在 SearchViewController 内显示，现在统一转发给当前分类子控制器
    func fetchSearchResults(keywords: String, requestedPage: Int, size: Int, sort: Int?) {
        // 记录当前分页信息
        self.currentPage = requestedPage
        self.isLoadingData = true
        defer { self.isLoadingData = false }
        // 将搜索请求交给当前分类视图控制器
        if let searchableVC = categoryViewControllers[currentCategoryIndex] as? Searchable {
            searchableVC.search(with: keywords)
        }
    }
}
