import UIKit
import SnapKit
import Kingfisher

class SearchUserTableViewCell: UITableViewCell {
    static let reuseIdentifier = "SearchUserTableViewCell"

    // MARK: UI
    private let avatarImageView: UIImageView = {
        let iv = UIImageView()
        iv.layer.cornerRadius = 24
        iv.clipsToBounds = true
        iv.contentMode = .scaleAspectFill
        iv.backgroundColor = UIColor(hex: "#EEEEEE")
        return iv
    }()

    private let nameLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 18, weight: .bold)
        lb.textColor = UIColor(hex: "#333333")
        return lb
    }()

    private let subtitleLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 14)
        lb.textColor = UIColor(hex: "#666666")
        return lb
    }()

    private let infoLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12)
        lb.textColor = UIColor(hex: "#999999")
        //下版本展示
        lb.isHidden = true
        return lb
    }()

    private let relationLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 13)
        lb.textColor = UIColor(hex: "#FF8E15")
        lb.isHidden = true
        return lb
    }()

    private let followButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("关注", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.titleLabel?.font = .systemFont(ofSize: 12, weight: .medium)
        btn.backgroundColor = UIColor(hex: "#FF8E15")
        btn.layer.cornerRadius = 13.5
        return btn
    }()

    private let liveBadge: UILabel = {
        let lb = UILabel()
        lb.text = "直播中"
        lb.font = .systemFont(ofSize: 10)
        lb.textColor = .white
        lb.backgroundColor = UIColor(hex: "#FF8E15")
        lb.layer.cornerRadius = 2
        lb.clipsToBounds = true
        lb.textAlignment = .center
        lb.isHidden = true
        return lb
    }()

    private let separator: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor(hex: "#EAEAEA")
        return v
    }()

    /// 当前关注状态
    private var isFollowing: Bool = false
    /// 当前用户ID，用于关注/取关操作
    private var currentUserId: String = ""
    /// 关注状态变化回调
    var onFollowStatusChanged: ((String, Bool) -> Void)?

    /// 粉丝数万化处理
    private func formatFollowersCount(_ count: Int) -> String {
        if count >= 10000 {
            let wan = Double(count) / 10000.0
            return String(format: "%.1f万", wan)
        } else {
            return "\(count)"
        }
    }

    // MARK: Init
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        backgroundColor = .white
        setupUI()
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    private func setupUI() {
        contentView.addSubview(avatarImageView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(subtitleLabel)
        contentView.addSubview(infoLabel)
        contentView.addSubview(relationLabel)
        contentView.addSubview(followButton)
        contentView.addSubview(liveBadge)
        contentView.addSubview(separator)

        avatarImageView.snp.remakeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.top.equalToSuperview().offset(10)
            make.size.equalTo(CGSize(width: 48, height: 48))
        }
        nameLabel.snp.remakeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(10)
            make.right.equalToSuperview().offset(-80)
            make.top.equalToSuperview().offset(10)
            make.height.equalTo(26)
        }
        subtitleLabel.snp.remakeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
            make.height.equalTo(17)
        }
        infoLabel.snp.remakeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
            make.height.equalTo(17)
        }
        relationLabel.snp.makeConstraints { make in
            // relationLabel.snp.makeConstraints { make in
            //     make.left.equalTo(nameLabel)
            //     make.top.equalTo(infoLabel.snp.bottom).offset(6)
            // }
        }
        followButton.snp.remakeConstraints { make in
            make.centerY.equalTo(avatarImageView)
            make.right.equalToSuperview().offset(-16)
            make.size.equalTo(CGSize(width: 62, height: 27))
        }
        liveBadge.snp.remakeConstraints { make in
            make.left.equalTo(avatarImageView)
            make.top.equalTo(avatarImageView.snp.bottom).offset(4)
            make.width.equalTo(48)
            make.height.equalTo(14)
        }
        separator.snp.remakeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-1)
            make.height.equalTo(1)
        }
    }

    func configure(with user: SearchUser) {
        if let url = URL(string: user.avatar) {
            avatarImageView.kf.setImage(with: url)
        }
        nameLabel.text = user.name

        // 隐藏中间内容label
        subtitleLabel.isHidden = true

        // 显示粉丝数和树小柒号，使用万化处理和14pt间距
        let followersText = formatFollowersCount(user.followers)
        let fansText = "粉丝：\(followersText)"
        let treeIdText = "树小柒号：\(user.customerAccount)"

        let attributedString = NSMutableAttributedString(string: fansText)
        let spacingString = NSAttributedString(string: "  ", attributes: [
            .kern: 14.0 // 14pt 间距
        ])
        let treeIdAttributedString = NSAttributedString(string: treeIdText)

        attributedString.append(spacingString)
        attributedString.append(treeIdAttributedString)
        infoLabel.attributedText = attributedString
        infoLabel.isHidden = false

        relationLabel.text = ""
        relationLabel.isHidden = true
        liveBadge.isHidden = !user.isLive

        // 保存用户信息
        currentUserId = user.treeId
        isFollowing = user.isFollowing
        updateFollowUI()

        followButton.removeTarget(nil, action: nil, for: .touchUpInside)
        followButton.addTarget(self, action: #selector(toggleFollow), for: .touchUpInside)
    }

    /// 设置分割线显示状态
    func setSeparatorHidden(_ hidden: Bool) {
        separator.isHidden = hidden
    }

    private func updateFollowUI() {
        followButton.setTitle(isFollowing ? "已关注" : "关注", for: .normal)
        if isFollowing {
            followButton.backgroundColor = .white
            followButton.setTitleColor(UIColor(hex: "#777777"), for: .normal)
            followButton.layer.borderWidth = 1
            followButton.layer.borderColor = UIColor(hex: "#E5E5E5").cgColor
        } else {
            followButton.backgroundColor = UIColor(hex: "#FF8F1F")
            followButton.setTitleColor(.white, for: .normal)
            followButton.layer.borderWidth = 0
            followButton.layer.borderColor = UIColor(hex: "#FF8F1F").cgColor
        }
    }

    @objc private func toggleFollow() {
        // 防止重复点击
        followButton.isEnabled = false

        let targetState = !isFollowing
        let type = targetState ? 1 : 2 // 1=关注，2=取关

        APIManager.shared.followUser‌(customerId: currentUserId, type: type, worksId: 0) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.followButton.isEnabled = true
                switch result {
                case .success:
                    // 更新状态
                    self.isFollowing = targetState
                    self.updateFollowUI()

                    // 通知外部状态变化
                    self.onFollowStatusChanged?(self.currentUserId, targetState)

                    // 显示提示
                    if let window = self.window {
                        self.showToast(targetState ? "关注成功" : "已取消关注", in: window)
                    }
                case .failure(let error):
                    print("关注操作失败: \(error.localizedDescription)")
                    // 显示错误提示
                    if let window = self.window {
                        self.showToast("操作失败，请稍后重试", in: window)
                    }
                }
            }
        }
    }

    // 简单的 Toast 提示
    private func showToast(_ message: String, in view: UIView) {
        let toast = UILabel()
        toast.text = message
        toast.textColor = .white
        toast.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        toast.font = .systemFont(ofSize: 14)
        toast.textAlignment = .center
        toast.layer.cornerRadius = 8
        toast.clipsToBounds = true

        view.addSubview(toast)
        toast.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            toast.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            toast.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            toast.widthAnchor.constraint(greaterThanOrEqualToConstant: 120),
            toast.heightAnchor.constraint(equalToConstant: 40)
        ])

        // 动画显示和隐藏
        toast.alpha = 0
        UIView.animate(withDuration: 0.3, animations: {
            toast.alpha = 1
        }) { _ in
            UIView.animate(withDuration: 0.3, delay: 1.5, options: [], animations: {
                toast.alpha = 0
            }) { _ in
                toast.removeFromSuperview()
            }
        }
    }
}
